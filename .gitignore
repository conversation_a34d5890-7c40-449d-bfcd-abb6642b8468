## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.
##
## Get latest from https://github.com/github/gitignore/blob/master/VisualStudio.gitignore

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# User-specific files (MonoDevelop/Xamarin Studio)
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio 2015 cache/options directory
.vs/
# Uncomment if you have tasks that create the project's static files in wwwroot
#wwwroot/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NUNIT
*.VisualState.xml
TestResult.xml

# Build Results of an ATL Project
[Dd]ebugPS/
[Rr]eleasePS/
dlldata.c

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/
**/Properties/launchSettings.json

*_i.c
*_p.c
*_i.h
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Chutzpah Test files
_Chutzpah*

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# TFS 2012 Local Workspace
$tf/

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

# JustCode is a .NET coding add-in
.JustCode

# TeamCity is a build add-in
_TeamCity*

# DotCover is a Code Coverage Tool
*.dotCover

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NCrunch
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*

# MightyMoose
*.mm.*
AutoTest.Net/

# Web workbench (sass)
.sass-cache/

# Installshield output folder
[Ee]xpress/

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish/

# Publish Web Output
*.[Pp]ublish.xml
*.azurePubxml
# TODO: Comment the next line if you want to checkin your web deploy settings
# but database connection strings (with potential passwords) will be unencrypted
*.pubxml
*.publishproj

# Microsoft Azure Web App publish settings. Comment the next line if you want to
# checkin your Azure Web App publish settings, but sensitive information contained
# in these scripts will be unencrypted
PublishScripts/

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/packages/*
# except build/, which is used as an MSBuild target.
!**/packages/build/
# Uncomment if necessary however generally it will be regenerated when needed
#!**/packages/repositories.config
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Microsoft Azure Build Output
csx/
*.build.csdef

# Microsoft Azure Emulator
ecf/
rcf/

# Windows Store app package directories and files
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt

# Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!*.[Cc]ache/

# Others
ClientBin/
~$*
*~
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings
orleans.codegen.cs

# Since there are multiple workflows, uncomment next line to ignore bower_components
# (https://github.com/github/gitignore/pull/1529#issuecomment-104372622)
#bower_components/

# RIA/Silverlight projects
Generated_Code/

# Backup & report files from converting an old project file
# to a newer Visual Studio version. Backup files are not needed,
# because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML
UpgradeLog*.htm

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings

# Microsoft Fakes
FakesAssemblies/

# GhostDoc plugin setting file
*.GhostDoc.xml

# Node.js Tools for Visual Studio
.ntvs_analysis.dat
node_modules/

# Typescript v1 declaration files
typings/

# Visual Studio 6 build log
*.plg

# Visual Studio 6 workspace options file
*.opt

# Visual Studio 6 auto-generated workspace file (contains which files were open etc.)
*.vbw

# Visual Studio LightSwitch build output
**/*.HTMLClient/GeneratedArtifacts
**/*.DesktopClient/GeneratedArtifacts
**/*.DesktopClient/ModelManifest.xml
**/*.Server/GeneratedArtifacts
**/*.Server/ModelManifest.xml
_Pvt_Extensions

# Paket dependency manager
.paket/paket.exe
paket-files/

# FAKE - F# Make
.fake/

# JetBrains Rider
.idea/
*.sln.iml

# CodeRush
.cr/

# Python Tools for Visual Studio (PTVS)
__pycache__/
*.pyc

# Cake - Uncomment if you are using it
# tools/**
# !tools/packages.config

# Telerik's JustMock configuration file
*.jmconfig

# BizTalk build output
*.btp.cs
*.btm.cs
*.odx.cs
*.xsd.cs

# macOS
.DS_Store
docs/_site/error-handling.html
docs/_site/favicon.ico
docs/_site/http-client-factory.html
docs/_site/index.html
docs/_site/index.json
docs/_site/logging.html
docs/_site/logo.svg
docs/_site/manifest.json
docs/_site/system-text-json.html
docs/_site/toc.html
docs/_site/toc.json
docs/_site/toc.pdf
docs/_site/xrefmap.yml
docs/_site/api/toc.html
docs/_site/api/toc.json
docs/_site/api/toc.pdf
docs/_site/api/Unsplasharp.Exceptions.ErrorContext.html
docs/_site/api/Unsplasharp.Exceptions.html
docs/_site/api/Unsplasharp.Exceptions.RateLimitInfo.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpAuthenticationException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpHttpException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpNetworkException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpNotFoundException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpParsingException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpRateLimitException.html
docs/_site/api/Unsplasharp.Exceptions.UnsplasharpTimeoutException.html
docs/_site/api/Unsplasharp.Extensions.html
docs/_site/api/Unsplasharp.Extensions.ServiceCollectionExtensions.html
docs/_site/api/Unsplasharp.Extensions.UnsplasharpOptions.html
docs/_site/api/Unsplasharp.html
docs/_site/api/Unsplasharp.Models.Badge.html
docs/_site/api/Unsplasharp.Models.Category.html
docs/_site/api/Unsplasharp.Models.CategoryLinks.html
docs/_site/api/Unsplasharp.Models.Collection.html
docs/_site/api/Unsplasharp.Models.CollectionLinks.html
docs/_site/api/Unsplasharp.Models.Exif.html
docs/_site/api/Unsplasharp.Models.html
docs/_site/api/Unsplasharp.Models.Location.html
docs/_site/api/Unsplasharp.Models.Photo.html
docs/_site/api/Unsplasharp.Models.PhotoLinks.html
docs/_site/api/Unsplasharp.Models.PhotoStats.html
docs/_site/api/Unsplasharp.Models.Position.html
docs/_site/api/Unsplasharp.Models.ProfileImage.html
docs/_site/api/Unsplasharp.Models.StatsData.html
docs/_site/api/Unsplasharp.Models.StatsHistorical.html
docs/_site/api/Unsplasharp.Models.StatsValue.html
docs/_site/api/Unsplasharp.Models.UnplashMonthlyStats.html
docs/_site/api/Unsplasharp.Models.UnplashTotalStats.html
docs/_site/api/Unsplasharp.Models.Urls.html
docs/_site/api/Unsplasharp.Models.User.html
docs/_site/api/Unsplasharp.Models.UserLinks.html
docs/_site/api/Unsplasharp.Models.UserStats.html
docs/_site/api/Unsplasharp.UnsplasharpClient.html
docs/_site/api/Unsplasharp.UnsplasharpClient.OrderBy.html
docs/_site/api/Unsplasharp.UnsplasharpClient.Orientation.html
docs/_site/api/Unsplasharp.UnsplasharpClient.Resolution.html
docs/_site/docs/downloading-a-photo.html
docs/_site/docs/getting-started.html
docs/_site/docs/introduction.html
docs/_site/docs/obtaining-an-api-key.html
docs/_site/docs/toc.html
docs/_site/docs/toc.json
docs/_site/docs/toc.pdf
docs/_site/public/architecture-I3QFYML2-2T2ZUHXO.min.js
docs/_site/public/architecture-I3QFYML2-2T2ZUHXO.min.js.map
docs/_site/public/architectureDiagram-UYN6MBPD-WBU2OYNU.min.js
docs/_site/public/architectureDiagram-UYN6MBPD-WBU2OYNU.min.js.map
docs/_site/public/blockDiagram-ZHA2E4KO-IZKTV5IP.min.js
docs/_site/public/blockDiagram-ZHA2E4KO-IZKTV5IP.min.js.map
docs/_site/public/bootstrap-icons-OCU552PF.woff
docs/_site/public/bootstrap-icons-X6UQXWUS.woff2
docs/_site/public/c4Diagram-6F5ED5ID-X53KYE5F.min.js
docs/_site/public/c4Diagram-6F5ED5ID-X53KYE5F.min.js.map
docs/_site/public/chunk-2YMHYP32.min.js
docs/_site/public/chunk-2YMHYP32.min.js.map
docs/_site/public/chunk-3Z74ZUXG.min.js
docs/_site/public/chunk-3Z74ZUXG.min.js.map
docs/_site/public/chunk-5IIW54K6.min.js
docs/_site/public/chunk-5IIW54K6.min.js.map
docs/_site/public/chunk-6YMKSKZH.min.js
docs/_site/public/chunk-6YMKSKZH.min.js.map
docs/_site/public/chunk-33FU46FA.min.js
docs/_site/public/chunk-33FU46FA.min.js.map
docs/_site/public/chunk-54U54PUP.min.js
docs/_site/public/chunk-54U54PUP.min.js.map
docs/_site/public/chunk-AUO2PXKS.min.js
docs/_site/public/chunk-AUO2PXKS.min.js.map
docs/_site/public/chunk-BD4P4Z7J.min.js
docs/_site/public/chunk-BD4P4Z7J.min.js.map
docs/_site/public/chunk-BIJFJY5F.min.js
docs/_site/public/chunk-BIJFJY5F.min.js.map
docs/_site/public/chunk-C7DS3QYJ.min.js
docs/_site/public/chunk-C7DS3QYJ.min.js.map
docs/_site/public/chunk-CLIYZZ5Y.min.js
docs/_site/public/chunk-CLIYZZ5Y.min.js.map
docs/_site/public/chunk-CM5D5KZN.min.js
docs/_site/public/chunk-CM5D5KZN.min.js.map
docs/_site/public/chunk-CXRPJJJE.min.js
docs/_site/public/chunk-CXRPJJJE.min.js.map
docs/_site/public/chunk-DTUU2GN4.min.js
docs/_site/public/chunk-DTUU2GN4.min.js.map
docs/_site/public/chunk-EDJWACL4.min.js
docs/_site/public/chunk-EDJWACL4.min.js.map
docs/_site/public/chunk-EKP7MBOP.min.js
docs/_site/public/chunk-EKP7MBOP.min.js.map
docs/_site/public/chunk-I4ZXTPQC.min.js
docs/_site/public/chunk-I4ZXTPQC.min.js.map
docs/_site/public/chunk-IJ4BRSPX.min.js
docs/_site/public/chunk-IJ4BRSPX.min.js.map
docs/_site/public/chunk-IQQ46AC6.min.js
docs/_site/public/chunk-IQQ46AC6.min.js.map
docs/_site/public/chunk-ISDTAGDN.min.js
docs/_site/public/chunk-ISDTAGDN.min.js.map
docs/_site/public/chunk-JL3VILNY.min.js
docs/_site/public/chunk-JL3VILNY.min.js.map
docs/_site/public/chunk-N4YULA37.min.js
docs/_site/public/chunk-N4YULA37.min.js.map
docs/_site/public/chunk-N6ME3NZU.min.js
docs/_site/public/chunk-N6ME3NZU.min.js.map
docs/_site/public/chunk-OSRY5VT3.min.js
docs/_site/public/chunk-OSRY5VT3.min.js.map
docs/_site/public/chunk-OZ2RCKQJ.min.js
docs/_site/public/chunk-OZ2RCKQJ.min.js.map
docs/_site/public/chunk-PDS7545E.min.js
docs/_site/public/chunk-PDS7545E.min.js.map
docs/_site/public/chunk-PYPO7LRM.min.js
docs/_site/public/chunk-PYPO7LRM.min.js.map
docs/_site/public/chunk-TLYS76Q7.min.js
docs/_site/public/chunk-TLYS76Q7.min.js.map
docs/_site/public/chunk-U3SD26FK.min.js
docs/_site/public/chunk-U3SD26FK.min.js.map
docs/_site/public/chunk-U4DUTLYF.min.js
docs/_site/public/chunk-U4DUTLYF.min.js.map
docs/_site/public/chunk-UEFJDIUO.min.js
docs/_site/public/chunk-UEFJDIUO.min.js.map
docs/_site/public/chunk-V55NTXQN.min.js
docs/_site/public/chunk-V55NTXQN.min.js.map
docs/_site/public/chunk-WMZJ2DJX.min.js
docs/_site/public/chunk-WMZJ2DJX.min.js.map
docs/_site/public/chunk-WXIN66R4.min.js
docs/_site/public/chunk-WXIN66R4.min.js.map
docs/_site/public/classDiagram-LNE6IOMH-VZ67B4ZP.min.js
docs/_site/public/classDiagram-LNE6IOMH-VZ67B4ZP.min.js.map
docs/_site/public/classDiagram-v2-MQ7JQ4JX-4JTAVB6L.min.js
docs/_site/public/classDiagram-v2-MQ7JQ4JX-4JTAVB6L.min.js.map
docs/_site/public/dagre-4EVJKHTY-MHPLGZHX.min.js
docs/_site/public/dagre-4EVJKHTY-MHPLGZHX.min.js.map
docs/_site/public/diagram-QW4FP2JN-UOF7FAFC.min.js
docs/_site/public/diagram-QW4FP2JN-UOF7FAFC.min.js.map
docs/_site/public/docfx.min.css
docs/_site/public/docfx.min.css.map
docs/_site/public/docfx.min.js
docs/_site/public/docfx.min.js.map
docs/_site/public/erDiagram-6RL3IURR-PEYW6AVI.min.js
docs/_site/public/erDiagram-6RL3IURR-PEYW6AVI.min.js.map
docs/_site/public/es-4I4X6RME.min.js
docs/_site/public/es-4I4X6RME.min.js.map
docs/_site/public/flowDiagram-7ASYPVHJ-DABBKNEC.min.js
docs/_site/public/flowDiagram-7ASYPVHJ-DABBKNEC.min.js.map
docs/_site/public/ganttDiagram-NTVNEXSI-JVQ2N4MZ.min.js
docs/_site/public/ganttDiagram-NTVNEXSI-JVQ2N4MZ.min.js.map
docs/_site/public/gitGraph-YCYPL57B-3XOJ53I6.min.js
docs/_site/public/gitGraph-YCYPL57B-3XOJ53I6.min.js.map
docs/_site/public/gitGraphDiagram-NRZ2UAAF-WVTRWY3E.min.js
docs/_site/public/gitGraphDiagram-NRZ2UAAF-WVTRWY3E.min.js.map
docs/_site/public/info-46DW6VJ7-RDUIJSMX.min.js
docs/_site/public/info-46DW6VJ7-RDUIJSMX.min.js.map
docs/_site/public/infoDiagram-A4XQUW5V-SKLVFWJI.min.js
docs/_site/public/infoDiagram-A4XQUW5V-SKLVFWJI.min.js.map
docs/_site/public/journeyDiagram-G5WM74LC-AHZ7GKR5.min.js
docs/_site/public/journeyDiagram-G5WM74LC-AHZ7GKR5.min.js.map
docs/_site/public/kanban-definition-QRCXZQQD-MKSHYOCX.min.js
docs/_site/public/kanban-definition-QRCXZQQD-MKSHYOCX.min.js.map
docs/_site/public/katex-ROPKEHCO.min.js
docs/_site/public/katex-ROPKEHCO.min.js.map
docs/_site/public/lunr.ar-A6ZT2INA.min.js
docs/_site/public/lunr.ar-A6ZT2INA.min.js.map
docs/_site/public/lunr.da-WWM276CR.min.js
docs/_site/public/lunr.da-WWM276CR.min.js.map
docs/_site/public/lunr.de-XXPRKDAY.min.js
docs/_site/public/lunr.de-XXPRKDAY.min.js.map
docs/_site/public/lunr.du-NO4L2LL3.min.js
docs/_site/public/lunr.du-NO4L2LL3.min.js.map
docs/_site/public/lunr.el-5ZSSJVMA.min.js
docs/_site/public/lunr.el-5ZSSJVMA.min.js.map
docs/_site/public/lunr.es-ZH6Q76E6.min.js
docs/_site/public/lunr.es-ZH6Q76E6.min.js.map
docs/_site/public/lunr.fi-S7WJSBCP.min.js
docs/_site/public/lunr.fi-S7WJSBCP.min.js.map
docs/_site/public/lunr.fr-H2QNBELV.min.js
docs/_site/public/lunr.fr-H2QNBELV.min.js.map
docs/_site/public/lunr.he-TTLAK4MN.min.js
docs/_site/public/lunr.he-TTLAK4MN.min.js.map
docs/_site/public/lunr.hi-PWWMAGLU.min.js
docs/_site/public/lunr.hi-PWWMAGLU.min.js.map
docs/_site/public/lunr.hu-DLG2DSVM.min.js
docs/_site/public/lunr.hu-DLG2DSVM.min.js.map
docs/_site/public/lunr.hy-FFQJAR7M.min.js
docs/_site/public/lunr.hy-FFQJAR7M.min.js.map
docs/_site/public/lunr.it-VQNLJLPR.min.js
docs/_site/public/lunr.it-VQNLJLPR.min.js.map
docs/_site/public/lunr.ja-J6QHZSR2.min.js
docs/_site/public/lunr.ja-J6QHZSR2.min.js.map
docs/_site/public/lunr.jp-M45D3XJE.min.js
docs/_site/public/lunr.jp-M45D3XJE.min.js.map
docs/_site/public/lunr.kn-ASLXFRTC.min.js
docs/_site/public/lunr.kn-ASLXFRTC.min.js.map
docs/_site/public/lunr.ko-RHF2BDE4.min.js
docs/_site/public/lunr.ko-RHF2BDE4.min.js.map
docs/_site/public/lunr.nl-2BITG354.min.js
docs/_site/public/lunr.nl-2BITG354.min.js.map
docs/_site/public/lunr.no-WPLSHWFO.min.js
docs/_site/public/lunr.no-WPLSHWFO.min.js.map
docs/_site/public/lunr.pt-V2XEBELC.min.js
docs/_site/public/lunr.pt-V2XEBELC.min.js.map
docs/_site/public/lunr.ro-O76266FJ.min.js
docs/_site/public/lunr.ro-O76266FJ.min.js.map
docs/_site/public/lunr.ru-G56UDXYH.min.js
docs/_site/public/lunr.ru-G56UDXYH.min.js.map
docs/_site/public/lunr.sa-LD5PRAIS.min.js
docs/_site/public/lunr.sa-LD5PRAIS.min.js.map
docs/_site/public/lunr.sv-7VRY4UDB.min.js
docs/_site/public/lunr.sv-7VRY4UDB.min.js.map
docs/_site/public/lunr.ta-OWB7AURB.min.js
docs/_site/public/lunr.ta-OWB7AURB.min.js.map
docs/_site/public/lunr.te-JGGL3BFP.min.js
docs/_site/public/lunr.te-JGGL3BFP.min.js.map
docs/_site/public/lunr.th-O4JBL3IY.min.js
docs/_site/public/lunr.th-O4JBL3IY.min.js.map
docs/_site/public/lunr.tr-WXUV733C.min.js
docs/_site/public/lunr.tr-WXUV733C.min.js.map
docs/_site/public/lunr.vi-3U4A337N.min.js
docs/_site/public/lunr.vi-3U4A337N.min.js.map
docs/_site/public/main.css
docs/_site/public/main.js
docs/_site/public/mermaid.core-QWHI4VJR.min.js
docs/_site/public/mermaid.core-QWHI4VJR.min.js.map
docs/_site/public/mindmap-definition-GWI6TPTV-XCX7U2FR.min.js
docs/_site/public/mindmap-definition-GWI6TPTV-XCX7U2FR.min.js.map
docs/_site/public/packet-W2GHVCYJ-ZZMTAWKW.min.js
docs/_site/public/packet-W2GHVCYJ-ZZMTAWKW.min.js.map
docs/_site/public/pie-BEWT4RHE-VFWRUT6J.min.js
docs/_site/public/pie-BEWT4RHE-VFWRUT6J.min.js.map
docs/_site/public/pieDiagram-YF2LJOPJ-ITGVNBO2.min.js
docs/_site/public/pieDiagram-YF2LJOPJ-ITGVNBO2.min.js.map
docs/_site/public/quadrantDiagram-OS5C2QUG-BN35C5UH.min.js
docs/_site/public/quadrantDiagram-OS5C2QUG-BN35C5UH.min.js.map
docs/_site/public/requirementDiagram-MIRIMTAZ-CXICLXCG.min.js
docs/_site/public/requirementDiagram-MIRIMTAZ-CXICLXCG.min.js.map
docs/_site/public/sankeyDiagram-Y46BX6SQ-LTJNBPUP.min.js
docs/_site/public/sankeyDiagram-Y46BX6SQ-LTJNBPUP.min.js.map
docs/_site/public/search-worker.min.js
docs/_site/public/search-worker.min.js.map
docs/_site/public/sequenceDiagram-G6AWOVSC-UJVWCU2P.min.js
docs/_site/public/sequenceDiagram-G6AWOVSC-UJVWCU2P.min.js.map
docs/_site/public/stateDiagram-MAYHULR4-UPNPJ5ZA.min.js
docs/_site/public/stateDiagram-MAYHULR4-UPNPJ5ZA.min.js.map
docs/_site/public/stateDiagram-v2-4JROLMXI-COTI64PW.min.js
docs/_site/public/stateDiagram-v2-4JROLMXI-COTI64PW.min.js.map
docs/_site/public/tex-svg-full-SL33OL2J.min.js
docs/_site/public/tex-svg-full-SL33OL2J.min.js.map
docs/_site/public/timeline-definition-U7ZMHBDA-I7GF7M6N.min.js
docs/_site/public/timeline-definition-U7ZMHBDA-I7GF7M6N.min.js.map
docs/_site/public/xychartDiagram-6QU3TZC5-MQVPM64I.min.js
docs/_site/public/xychartDiagram-6QU3TZC5-MQVPM64I.min.js.map
docs/_site/advanced-usage.html
docs/_site/api-reference.html
docs/_site/code-examples.html
docs/_site/migration-guide.html
docs/_site/models-reference.html
docs/_site/navigation.html
docs/_site/quick-reference.html
docs/_site/table-of-contents.html
docs/_site/testing-best-practices.html
